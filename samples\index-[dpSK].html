<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BellaBingo - Bingo Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        
        .sidebar {
            transition: all 0.3s ease;
        }
        
        .bingo-ball {
            transition: all 0.3s ease;
        }
        
        .bingo-ball.called {
            transform: scale(0.9);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
        }
        
        .cartella-number.called {
            background-color: #dc2626;
            color: white;
        }
        
        .winner-animation {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-white w-64 md:w-72 border-r border-gray-200 flex flex-col">
            <div class="gradient-bg p-4 flex items-center">
                <div class="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <h1 class="text-white font-bold text-xl">BellaBingo</h1>
            </div>
            
            <div class="flex-1 overflow-y-auto py-4">
                <nav>
                    <div class="px-4 mb-6">
                        <h3 class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-4">Dashboard</h3>
                        <ul>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg bg-red-50 text-red-600 font-medium">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                                    </svg>
                                    Game Control
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                    </svg>
                                    Game History
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                                    </svg>
                                    Sales Analytics
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="px-4 mb-6">
                        <h3 class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-4">Bingo Tools</h3>
                        <ul>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                    Cartella Manager
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                    </svg>
                                    Number Generator
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="px-4">
                        <h3 class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-4">Settings</h3>
                        <ul>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                                    </svg>
                                    Game Settings
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
                                    </svg>
                                    User Profile
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
            
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" class="w-10 h-10 rounded-full mr-3" alt="User">
                    <div>
                        <p class="font-medium text-gray-800">Maria Rossi</p>
                        <p class="text-sm text-gray-500">Game Manager</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
                <div class="flex items-center">
                    <button class="md:hidden mr-4 text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <h2 class="text-xl font-semibold text-gray-800">Game Control Panel</h2>
                </div>
                <div class="flex items-center">
                    <button class="p-2 rounded-full bg-gray-100 text-gray-500 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <button class="p-2 rounded-full bg-gray-100 text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                        </svg>
                    </button>
                </div>
            </header>
            
            <main class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Current Game</p>
                                <p class="text-2xl font-semibold text-gray-800 mt-1">BINGO-75</p>
                            </div>
                            <div class="p-3 rounded-lg bg-red-50 text-red-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Active
                            </span>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Numbers Called</p>
                                <p class="text-2xl font-semibold text-gray-800 mt-1"><span id="called-count">12</span>/75</p>
                            </div>
                            <div class="p-3 rounded-lg bg-blue-50 text-blue-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 16%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Active Cartellas</p>
                                <p class="text-2xl font-semibold text-gray-800 mt-1">48</p>
                            </div>
                            <div class="p-3 rounded-lg bg-green-50 text-green-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500"><span class="text-green-600 font-medium">+12%</span> from last game</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Current Prize</p>
                                <p class="text-2xl font-semibold text-gray-800 mt-1">$1,250</p>
                            </div>
                            <div class="p-3 rounded-lg bg-purple-50 text-purple-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500">Next prize at <span class="font-medium">25 numbers</span></p>
                        </div>
                    </div>
                </div>
                
                <!-- Game Control Section -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Bingo Board -->
                    <div class="lg:col-span-2 bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">BINGO-75 Board</h3>
                            <div class="flex space-x-2">
                                <button id="call-number" class="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                    Call Number
                                </button>
                                <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                    Pause Game
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <div class="bg-red-600 text-white rounded-lg p-4 flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium">Last Number Called</p>
                                    <p id="last-called" class="text-3xl font-bold mt-1">B12</p>
                                </div>
                                <button id="repeat-call" class="p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 focus:outline-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m9.9-2.828a9 9 0 001.414 1.414" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-5 gap-3">
                            <div class="text-center">
                                <p class="text-lg font-bold text-red-600 mb-2">B</p>
                                <div id="b-col" class="space-y-2">
                                    <!-- Numbers will be inserted here by JS -->
                                </div>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-red-600 mb-2">I</p>
                                <div id="i-col" class="space-y-2">
                                    <!-- Numbers will be inserted here by JS -->
                                </div>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-red-600 mb-2">N</p>
                                <div id="n-col" class="space-y-2">
                                    <!-- Numbers will be inserted here by JS -->
                                </div>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-red-600 mb-2">G</p>
                                <div id="g-col" class="space-y-2">
                                    <!-- Numbers will be inserted here by JS -->
                                </div>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-red-600 mb-2">O</p>
                                <div id="o-col" class="space-y-2">
                                    <!-- Numbers will be inserted here by JS -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cartella Preview -->
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Sample Cartella</h3>
                            <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                New Cartella
                            </button>
                        </div>
                        
                        <div class="bg-red-50 rounded-lg p-4 mb-4">
                            <div class="grid grid-cols-5 gap-2">
                                <div class="text-center font-bold text-red-600 py-2">B</div>
                                <div class="text-center font-bold text-red-600 py-2">I</div>
                                <div class="text-center font-bold text-red-600 py-2">N</div>
                                <div class="text-center font-bold text-red-600 py-2">G</div>
                                <div class="text-center font-bold text-red-600 py-2">O</div>
                            </div>
                            
                            <div id="cartella-grid" class="grid grid-cols-5 gap-2 mt-2">
                                <!-- Cartella numbers will be inserted here by JS -->
                            </div>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                            <div class="flex items-start">
                                <div class="p-2 bg-yellow-100 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-yellow-800">Win Detection</p>
                                    <p id="win-status" class="text-sm text-yellow-600 mt-1">No winning pattern detected yet.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Numbers & Game History -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Numbers -->
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">Recently Called Numbers</h3>
                        <div id="recent-numbers" class="flex flex-wrap gap-2">
                            <!-- Recent numbers will be inserted here by JS -->
                        </div>
                    </div>
                    
                    <!-- Game History -->
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Games</h3>
                            <button class="text-sm font-medium text-red-600 hover:text-red-700">View All</button>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-green-100 rounded-lg mr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">BINGO-75 #2489</p>
                                        <p class="text-sm text-gray-500">Completed 12 mins ago</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-800">$1,100</p>
                                    <p class="text-sm text-gray-500">42 numbers</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-green-100 rounded-lg mr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">BINGO-75 #2488</p>
                                        <p class="text-sm text-gray-500">Completed 1 hour ago</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-800">$950</p>
                                    <p class="text-sm text-gray-500">38 numbers</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-red-100 rounded-lg mr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707-7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">BINGO-75 #2487</p>
                                        <p class="text-sm text-gray-500">Cancelled 2 hours ago</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-800">-</p>
                                    <p class="text-sm text-gray-500">22 numbers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize game state
            const gameState = {
                calledNumbers: [],
                lastCalled: null,
                cartellaNumbers: generateCartella(),
                patterns: {
                    row1: [0, 1, 2, 3, 4],
                    row2: [5, 6, 7, 8, 9],
                    row3: [10, 11, 12, 13, 14],
                    row4: [15, 16, 17, 18, 19],
                    row5: [20, 21, 22, 23, 24],
                    column1: [0, 5, 10, 15, 20],
                    column2: [1, 6, 11, 16, 21],
                    column3: [2, 7, 12, 17, 22],
                    column4: [3, 8, 13, 18, 23],
                    column5: [4, 9, 14, 19, 24],
                    diagonal1: [0, 6, 12, 18, 24],
                    diagonal2: [4, 8, 12, 16, 20],
                    fourCorners: [0, 4, 20, 24],
                    fullHouse: Array.from({length: 25}, (_, i) => i)
                }
            };
            
            // Generate BINGO board
            generateBingoBoard();
            
            // Generate sample cartella
            renderCartella();
            
            // Event listeners
            document.getElementById('call-number').addEventListener('click', callNumber);
            document.getElementById('repeat-call').addEventListener('click', repeatLastCall);
            
            // Generate BINGO board numbers
            function generateBingoBoard() {
                const columns = {
                    'B': [1, 15],
                    'I': [16, 30],
                    'N': [31, 45],
                    'G': [46, 60],
                    'O': [61, 75]
                };
                
                for (const [letter, range] of Object.entries(columns)) {
                    const container = document.getElementById(`${letter.toLowerCase()}-col`);
                    
                    for (let i = range[0]; i <= range[1]; i++) {
                        const ball = document.createElement('div');
                        ball.className = 'bingo-ball w-12 h-12 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center mx-auto cursor-pointer font-medium';
                        ball.textContent = i;
                        ball.dataset.number = `${letter}${i}`;
                        ball.dataset.value = i;
                        
                        ball.addEventListener('click', function() {
                            toggleNumberCall(this.dataset.number);
                        });
                        
                        container.appendChild(ball);
                    }
                }
            }
            
            // Generate cartella numbers
            function generateCartella() {
                const columns = {
                    'B': [1, 15],
                    'I': [16, 30],
                    'N': [31, 45],
                    'G': [46, 60],
                    'O': [61, 75]
                };
                
                const cartella = [];
                
                for (let col = 0; col < 5; col++) {
                    const letter = Object.keys(columns)[col];
                    const [min, max] = columns[letter];
                    const colNumbers = [];
                    
                    // Generate 5 unique numbers for this column
                    while (colNumbers.length < 5) {
                        const num = Math.floor(Math.random() * (max - min + 1)) + min;
                        if (!colNumbers.includes(num)) {
                            colNumbers.push(num);
                        }
                    }
                    
                    // Sort the numbers
                    colNumbers.sort((a, b) => a - b);
                    
                    // Add to cartella (middle cell is free in N column)
                    for (let row = 0; row < 5; row++) {
                        if (col === 2 && row === 2) { // Center cell
                            cartella.push({ letter: 'N', number: 'FREE', value: 0, isFree: true });
                        } else {
                            cartella.push({ 
                                letter, 
                                number: `${letter}${colNumbers[row]}`, 
                                value: colNumbers[row],
                                isFree: false
                            });
                        }
                    }
                }
                
                return cartella;
            }
            
            // Render cartella
            function renderCartella() {
                const container = document.getElementById('cartella-grid');
                container.innerHTML = '';
                
                gameState.cartellaNumbers.forEach((num, index) => {
                    const cell = document.createElement('div');
                    cell.className = `cartella-number bg-white rounded-md p-2 text-center font-medium ${num.isFree ? 'bg-red-100 text-red-600' : ''}`;
                    
                    if (num.isFree) {
                        cell.textContent = 'FREE';
                    } else {
                        cell.textContent = num.value;
                        cell.dataset.number = num.number;
                        
                        // Check if this number has been called
                        if (gameState.calledNumbers.includes(num.number)) {
                            cell.classList.add('called');
                        }
                    }
                    
                    container.appendChild(cell);
                });
                
                // Check for wins
                checkForWins();
            }
            
            // Call a random number
            function callNumber() {
                if (gameState.calledNumbers.length >= 75) {
                    alert('All numbers have been called!');
                    return;
                }
                
                const letters = ['B', 'I', 'N', 'G', 'O'];
                let availableNumbers = [];
                
                // Find all uncalled numbers
                for (const letter of letters) {
                    const min = letter === 'B' ? 1 : 
                               letter === 'I' ? 16 : 
                               letter === 'N' ? 31 : 
                               letter === 'G' ? 46 : 61;
                    const max = min + 14;
                    
                    for (let i = min; i <= max; i++) {
                        const num = `${letter}${i}`;
                        if (!gameState.calledNumbers.includes(num)) {
                            availableNumbers.push(num);
                        }
                    }
                }
                
                if (availableNumbers.length === 0) return;
                
                // Select random number
                const randomIndex = Math.floor(Math.random() * availableNumbers.length);
                const calledNumber = availableNumbers[randomIndex];
                
                // Update game state
                gameState.calledNumbers.push(calledNumber);
                gameState.lastCalled = calledNumber;
                
                // Update UI
                updateCalledNumber(calledNumber);
                updateRecentNumbers();
                renderCartella();
                
                // Play sound (in a real app)
                // playSound('number-called');
            }
            
            // Repeat last call
            function repeatLastCall() {
                if (!gameState.lastCalled) return;
                
                // In a real app, this would play an audio announcement
                console.log(`Repeating last call: ${gameState.lastCalled}`);
                
                // Highlight the number briefly
                const lastCalledElement = document.getElementById('last-called');
                lastCalledElement.classList.add('animate-pulse');
                setTimeout(() => {
                    lastCalledElement.classList.remove('animate-pulse');
                }, 1000);
            }
            
            // Toggle number call (for manual calling)
            function toggleNumberCall(number) {
                const index = gameState.calledNumbers.indexOf(number);
                
                if (index === -1) {
                    // Number not called yet - call it
                    gameState.calledNumbers.push(number);
                    gameState.lastCalled = number;
                } else {
                    // Number already called - undo call
                    gameState.calledNumbers.splice(index, 1);
                    if (gameState.lastCalled === number) {
                        gameState.lastCalled = gameState.calledNumbers.length > 0 ? 
                            gameState.calledNumbers[gameState.calledNumbers.length - 1] : 
                            null;
                    }
                }
                
                // Update UI
                updateCalledNumber(number);
                updateRecentNumbers();
                renderCartella();
            }
            
            // Update called number display
            function updateCalledNumber(number) {
                // Update last called display
                document.getElementById('last-called').textContent = number;
                
                // Update called count
                document.getElementById('called-count').textContent = gameState.calledNumbers.length;
                
                // Update ball appearance
                const ball = document.querySelector(`.bingo-ball[data-number="${number}"]`);
                if (ball) {
                    if (gameState.calledNumbers.includes(number)) {
                        ball.classList.add('called', 'bg-red-100', 'border-red-300');
                    } else {
                        ball.classList.remove('called', 'bg-red-100', 'border-red-300');
                    }
                }
            }
            
            // Update recent numbers display
            function updateRecentNumbers() {
                const container = document.getElementById('recent-numbers');
                container.innerHTML = '';
                
                // Show last 10 called numbers in reverse order
                const recentNumbers = [...gameState.calledNumbers].reverse().slice(0, 10);
                
                recentNumbers.forEach(num => {
                    const tag = document.createElement('span');
                    tag.className = 'inline-flex items-center px-3 py-1 rounded-full bg-red-50 text-red-700 text-sm font-medium';
                    tag.textContent = num;
                    container.appendChild(tag);
                });
            }
            
            // Check for winning patterns
            function checkForWins() {
                const winStatus = document.getElementById('win-status');
                let hasWin = false;
                
                // Check each pattern
                for (const [patternName, indexes] of Object.entries(gameState.patterns)) {
                    const isWinning = indexes.every(index => {
                        // Free space always counts
                        if (gameState.cartellaNumbers[index].isFree) return true;
                        
                        // Check if number was called
                        return gameState.calledNumbers.includes(gameState.cartellaNumbers[index].number);
                    });
                    
                    if (isWinning) {
                        hasWin = true;
                        winStatus.textContent = `Winning pattern: ${patternName.replace(/([A-Z])/g, ' $1').trim()}`;
                        winStatus.classList.add('winner-animation');
                        
                        // In a real app, you would highlight the winning pattern
                        console.log(`Winning pattern: ${patternName}`);
                    }
                }
                
                if (!hasWin) {
                    winStatus.textContent = 'No winning pattern detected yet.';
                    winStatus.classList.remove('winner-animation');
                }
            }
        });
    </script>
</body>
</html>