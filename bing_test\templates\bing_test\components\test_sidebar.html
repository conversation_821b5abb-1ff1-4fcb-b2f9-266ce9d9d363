<!-- Test Sidebar -->
<div class="sidebar bg-white w-64 md:w-72 border-r border-gray-200 flex flex-col">
    <!-- Logo/Brand Section -->
    <div class="gradient-bg p-4 flex items-center">
        <div class="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
        </div>
        <div>
            <h1 class="text-white font-bold text-xl">BellaBingo</h1>
            <p class="text-red-100 text-sm">Test Environment</p>
        </div>
    </div>
    
    <!-- Navigation Menu -->
    <div class="flex-1 overflow-y-auto py-4">
        <nav>
            <!-- Main Navigation -->
            <div class="px-4 mb-6">
                <h3 class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-4">Navigation</h3>
                <ul>
                    <li class="mb-2">
                        <a href="{% url 'bing_test:dashboard' %}" 
                           class="flex items-center px-3 py-2 rounded-lg transition-colors
                                  {% if request.resolver_match.url_name == 'dashboard' %}
                                      bg-red-50 text-red-600 font-medium
                                  {% else %}
                                      text-gray-600 hover:bg-gray-50 hover:text-red-600
                                  {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="{% url 'bing_test:start_game' %}" 
                           class="flex items-center px-3 py-2 rounded-lg transition-colors
                                  {% if request.resolver_match.url_name == 'start_game' %}
                                      bg-red-50 text-red-600 font-medium
                                  {% else %}
                                      text-gray-600 hover:bg-gray-50 hover:text-red-600
                                  {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                            </svg>
                            Start Game
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="{% url 'bing_test:cartellas' %}" 
                           class="flex items-center px-3 py-2 rounded-lg transition-colors
                                  {% if request.resolver_match.url_name == 'cartellas' %}
                                      bg-red-50 text-red-600 font-medium
                                  {% else %}
                                      text-gray-600 hover:bg-gray-50 hover:text-red-600
                                  {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            Cartellas
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="{% url 'bing_test:games' %}" 
                           class="flex items-center px-3 py-2 rounded-lg transition-colors
                                  {% if request.resolver_match.url_name == 'games' %}
                                      bg-red-50 text-red-600 font-medium
                                  {% else %}
                                      text-gray-600 hover:bg-gray-50 hover:text-red-600
                                  {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                            </svg>
                            Games (History)
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="{% url 'bing_test:sales' %}" 
                           class="flex items-center px-3 py-2 rounded-lg transition-colors
                                  {% if request.resolver_match.url_name == 'sales' %}
                                      bg-red-50 text-red-600 font-medium
                                  {% else %}
                                      text-gray-600 hover:bg-gray-50 hover:text-red-600
                                  {% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                            </svg>
                            Sales
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
    
    <!-- User Simulation Section -->
    <div class="p-4 border-t border-gray-200">
        <div class="mb-3">
            <h4 class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-2">Current Client</h4>
            {% if current_fake_client %}
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-red-500 to-yellow-500 flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">{{ current_fake_client.name|first }}</span>
                </div>
                <div>
                    <p class="font-medium text-gray-800">{{ current_fake_client.name }}</p>
                    <p class="text-sm text-gray-500">{{ current_fake_client.shop_name }}</p>
                </div>
            </div>
            {% else %}
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-800">No Client Selected</p>
                    <p class="text-sm text-gray-500">Select a client below</p>
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- User Switcher Dropdown -->
        <div class="relative" id="user-switcher">
            <button class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-left text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors" 
                    id="user-switcher-button">
                <div class="flex items-center justify-between">
                    <span>Switch Client</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </button>
            
            <!-- Dropdown Menu -->
            <div class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50 hidden max-h-48 overflow-y-auto" 
                 id="user-switcher-dropdown">
                {% for client in fake_clients %}
                <button class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors client-option" 
                        data-client-id="{{ client.id }}"
                        data-client-name="{{ client.name }}"
                        data-shop-name="{{ client.shop_name }}">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-gradient-to-r from-red-500 to-yellow-500 flex items-center justify-center mr-2">
                            <span class="text-white font-bold text-xs">{{ client.name|first }}</span>
                        </div>
                        <div>
                            <p class="font-medium">{{ client.name }}</p>
                            <p class="text-xs text-gray-500">{{ client.shop_name }}</p>
                        </div>
                    </div>
                </button>
                {% empty %}
                <div class="px-3 py-2 text-sm text-gray-500">No clients available</div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Balance Display -->
        {% if current_fake_client %}
        <div class="mt-3 p-2 bg-yellow-50 rounded-lg">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-yellow-800">Balance:</span>
                <span class="text-sm font-bold text-yellow-900">${{ current_fake_client.balance|floatformat:2 }}</span>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript for user switcher -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSwitcherButton = document.getElementById('user-switcher-button');
    const userSwitcherDropdown = document.getElementById('user-switcher-dropdown');
    const clientOptions = document.querySelectorAll('.client-option');
    
    if (userSwitcherButton && userSwitcherDropdown) {
        // Toggle dropdown
        userSwitcherButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userSwitcherDropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            userSwitcherDropdown.classList.add('hidden');
        });
        
        // Prevent dropdown from closing when clicking inside it
        userSwitcherDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
        
        // Handle client selection
        clientOptions.forEach(option => {
            option.addEventListener('click', function() {
                const clientId = this.dataset.clientId;
                const clientName = this.dataset.clientName;
                const shopName = this.dataset.shopName;
                
                // Send AJAX request to switch client
                fetch('{% url "bing_test:switch_client" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        'client_id': clientId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to update the UI
                        window.location.reload();
                    } else {
                        alert('Error switching client: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error switching client');
                });
                
                userSwitcherDropdown.classList.add('hidden');
            });
        });
    }
});
</script>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
