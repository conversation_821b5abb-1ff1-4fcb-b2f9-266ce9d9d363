from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from .models import Fake<PERSON>lient, FakeGame, FakeGameCartellaDetail
import json
import random
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_current_fake_client(request):
    """Get the current fake client from session"""
    client_id = request.session.get('fake_client_id')
    if client_id:
        try:
            return FakeClient.objects.get(id=client_id, is_active=True)
        except FakeClient.DoesNotExist:
            pass
    return None

def get_base_context(request):
    """Get base context data for all views"""
    current_fake_client = get_current_fake_client(request)
    fake_clients = FakeClient.objects.filter(is_active=True).order_by('name')

    return {
        'current_fake_client': current_fake_client,
        'fake_clients': fake_clients,
    }

# Dashboard View
def dashboard(request):
    context = get_base_context(request)

    # Add dashboard-specific data
    if context['current_fake_client']:
        recent_games = FakeGame.objects.filter(
            client=context['current_fake_client']
        ).order_by('-created_at')[:5]

        context.update({
            'recent_games': recent_games,
            'total_games': FakeGame.objects.filter(client=context['current_fake_client']).count(),
            'active_games': FakeGame.objects.filter(client=context['current_fake_client'], status='playing').count(),
        })

    return render(request, 'bing_test/dashboard.html', context)

# Start Game View
def start_game(request):
    from bingo_app.models import Cartella

    context = get_base_context(request)

    # Get all cartellas from database
    cartellas = Cartella.objects.all().order_by('cartella_number')

    # Add game-specific context
    context.update({
        'cartellas': cartellas,
        'total_cartellas': cartellas.count(),
        'default_bet_price': 20,
        'min_bet_price': 10,
        'bet_increment': 10,
    })

    return render(request, 'bing_test/game_interface.html', context)

# Cartellas View
def cartellas(request):
    from bingo_app.models import Cartella
    from django.core.paginator import Paginator
    from django.db.models import Q

    context = get_base_context(request)

    # Get all cartellas
    cartellas_list = Cartella.objects.all().order_by('cartella_number')

    # Handle search
    search_query = request.GET.get('search', '').strip()
    if search_query:
        # Very specific search - exact matches preferred
        search_conditions = Q()

        # 1. Exact cartella number match (highest priority)
        search_conditions |= Q(cartella_number__exact=search_query)

        # 2. Exact cartella ID match
        search_conditions |= Q(cartella_id__exact=search_query)

        # 3. If search query is numeric, also check if it matches any number in the grid
        if search_query.isdigit():
            # Search for the exact number in the grid JSON
            search_conditions |= Q(grid__icontains=f'"{search_query}"')

        # 4. If search query looks like a BINGO call (e.g., "B12", "N34")
        if len(search_query) >= 2 and search_query[0].upper() in ['B', 'I', 'N', 'G', 'O'] and search_query[1:].isdigit():
            # Search for the exact BINGO call in the grid
            search_conditions |= Q(grid__icontains=f'"{search_query.upper()}"')

        # Apply the search filter
        cartellas_list = cartellas_list.filter(search_conditions)

    # Pagination - 50 cartellas per page
    paginator = Paginator(cartellas_list, 52)
    page_number = request.GET.get('page')
    cartellas = paginator.get_page(page_number)

    # Add pagination info to context
    context.update({
        'cartellas': cartellas,
        'search_query': search_query,
        'total_cartellas': paginator.count,
        'is_admin': context['current_fake_client'].is_superuser if context['current_fake_client'] else False,
    })

    return render(request, 'bing_test/cartellas.html', context)

# Games (History) View
def games(request):
    context = get_base_context(request)

    if context['current_fake_client']:
        all_games = FakeGame.objects.filter(
            client=context['current_fake_client']
        ).order_by('-created_at')

        context.update({
            'all_games': all_games,
        })

    return render(request, 'bing_test/games.html', context)

# Sales View
def sales(request):
    context = get_base_context(request)
    return render(request, 'bing_test/sales.html', context)

# Switch Client View (AJAX)
@require_POST
def switch_client(request):
    try:
        data = json.loads(request.body)
        client_id = data.get('client_id')

        if client_id:
            client = get_object_or_404(FakeClient, id=client_id, is_active=True)
            request.session['fake_client_id'] = client.id
            return JsonResponse({
                'success': True,
                'client_name': client.name,
                'shop_name': client.shop_name
            })
        else:
            # Clear the session
            request.session.pop('fake_client_id', None)
            return JsonResponse({'success': True})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

# Legacy view for backward compatibility
def bing_test(request):
    return dashboard(request)