from django.db import models
from bingo_app.models import Game, Cartella

from django.db import models

class SystemConfig(models.Model):
    system_charge_rate = models.FloatField(default=20.0)  # 20% percent
    client_cat_rate = models.FloatField(default=13.0)  # 13% percent
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"System Charge Rate: {self.system_charge_rate}% -|- Client Charge Rate: {self.client_cat_rate}%"

# Create your models here.
class FakeClient(models.Model):
    name = models.CharField(max_length=100)
    shop_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=20)
    address = models.TextField()
    email = models.EmailField(unique=True)
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)    
    is_superuser = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class Game(models.Model):
    client = models.ForeignKey(FakeClient, on_delete=models.CASCADE, related_name="games")
    status = models.CharField(max_length=20, choices=[('pending', 'Pending'), ('playing', 'Playing'), ('paused', 'Paused'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='pending')
    bet_price = models.DecimalField(max_digits=6, decimal_places=2) # Bet price per cartella(stake)
    winning_patterns = models.JSONField(default=list)  # e.g. ["row1", "column3"]
    sold_cartellas_count = models.PositiveIntegerField(default=0)
    winning_cartellas_count = models.PositiveIntegerField(default=0)
    called_numbers = models.JSONField(default=list)  # e.g. ["B-3", "I-16", "N-35"]
    started_at = models.DateTimeField(blank=True, null=True)
    ended_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    total_stake = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    system_charge = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    client_cat = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    prize_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def calculate_financials(self):
        system_config = SystemConfig.objects.latest('updated_at')
        self.total_stake = self.sold_cartellas_count * self.bet_price
        self.system_charge = self.total_stake * (system_config.system_charge_rate / 100)
        self.client_cat = self.total_stake * (system_config.client_cat_rate / 100)
        net_amount = self.total_stake - self.system_charge - self.client_cat
        if self.winning_cartellas_count > 0:
            self.prize_amount = net_amount / self.winning_cartellas_count
        else:
            self.prize_amount = 0
        
    def __str__(self):
        return f"Game #{self.id} by {self.client.name} ({self.status})"
    
class GameCartellaDetail(models.Model):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name="game_cartella_details")
    cartella = models.ForeignKey(Cartella, on_delete=models.CASCADE, related_name="game_cartella_details")
    is_winner = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Game #{self.game.id} - Cartella #{self.cartella.cartella_number} ({'Winner' if self.is_winner else 'Loser'})"