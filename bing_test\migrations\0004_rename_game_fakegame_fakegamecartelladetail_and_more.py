# Generated by Django 5.2.5 on 2025-08-12 12:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bing_test', '0003_fakeclient_is_superuser'),
        ('bingo_app', '0004_game'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='Game',
            new_name='FakeGame',
        ),
        migrations.CreateModel(
            name='FakeGameCartellaDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_winner', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cartella', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fakegame_registered_cartellas', to='bingo_app.cartella')),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fakegame_cartella_details', to='bing_test.fakegame')),
            ],
        ),
        migrations.DeleteModel(
            name='GameCartellaDetail',
        ),
    ]
