from decimal import Decimal
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from .utils import generate_unique_cartella, generate_bingo_board, check_bingo_claim
from .models import Cartella, Game, GameStatus
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages


@login_required
def generate_cartella_view(request):
    if not request.user.is_staff:
        return redirect('cartella_list')  # redirect unauthorized users

    count = int(request.GET.get('count', 50))
    for _ in range(count):
        generate_unique_cartella()

    return redirect('cartella_list')  

@login_required
def cartella_list_view(request):
    cartellas = Cartella.objects.order_by('cartella_number')[:100]  # Show latest 100
    for cartella in cartellas:
        # Convert first 25 characters of grid_hash to booleans
        cartella.grid_cells = [
            int(char, 16) % 2 == 0  # Example logic: even hex digit = active
            for char in cartella.grid_hash[:25]
        ]
    return render(request, 'bingo_app/cartella_list.html', {'cartellas': cartellas})
    # return render(request, 'bingo_app/cartella_list.html', {'cartellas': cartellas})


@login_required
def start_game_view(request):
    cartellas = Cartella.objects.all().order_by('number')
    if request.method == 'POST':
        selected_numbers = request.POST.getlist('cartellas')
        bet_price = Decimal(request.POST.get('bet_price'))
        winning_patterns = request.POST.getlist('winning_patterns')

        game = Game.objects.create(
            client=request.user,
            bet_price=bet_price,
            winning_patterns=winning_patterns,
            sold_cartellas_count=len(selected_numbers),
            status=GameStatus.PLAYING,
        )
        selected_cartellas = Cartella.objects.filter(number__in=selected_numbers)
        game.sold_cartellas.set(selected_cartellas)
        game.save()
        return redirect('play_game', game_id=game.id)

    return render(request, 'bingo/start_game.html', {'cartellas': cartellas})

@login_required
# def master_board_view(request, game_id):# Play Game
#     '''
#     Play Game, this shows the bingo board, play/pause buttons, and the called numbers.
#     '''
#     # 
#     board = generate_bingo_board()
#     # called_numbers = [5, 20, 34, 48, 73]  # example called numbers
    
#     game = Game.objects.get(pk=game_id)

#     if request.method == 'POST':
#         action = request.POST.get('action')

#         if action == 'claim_bingo':
#             cartella_num = int(request.POST.get('cartella_number'))
#             pattern = request.POST.get('pattern')
#             result = check_bingo_claim(game, cartella_num, pattern)

#             if result['success']:
#                 messages.success(request, result.get('message', 'Bingo! You won!'))
#             else:
#                 messages.error(request, result.get('error', 'Invalid Bingo claim.'))

#             return redirect('play_game', game_id=game.id)

#         # Handle other actions: draw_number, pause_game, resume_game...

#     # GET request - just render the page
#     context = {
#         'board': board,
#         # 'called_numbers': called_numbers
#         'game': game,
#         # other context vars...
#     }
    
#     return render(request, 'bingo_app/master_board.html', context)

def master_board_view(request):# Play Game
    '''
    Play Game, this shows the bingo board, play/pause buttons, and the called numbers.
    '''
    # 
    board = generate_bingo_board()
    # called_numbers = [5, 20, 34, 48, 73]  # example called numbers
    
    # game = Game.objects.get(pk=game_id)

    # if request.method == 'POST':
    #     action = request.POST.get('action')

    #     if action == 'claim_bingo':
    #         cartella_num = int(request.POST.get('cartella_number'))
    #         pattern = request.POST.get('pattern')
    #         result = check_bingo_claim(game, cartella_num, pattern)

    #         if result['success']:
    #             messages.success(request, result.get('message', 'Bingo! You won!'))
    #         else:
    #             messages.error(request, result.get('error', 'Invalid Bingo claim.'))

    #         return redirect('play_game', game_id=game.id)

        # Handle other actions: draw_number, pause_game, resume_game...

    # GET request - just render the page
    context = {
        'board': board,
        # 'called_numbers': called_numbers
        # 'game': game,
        # other context vars...
    }
    
    return render(request, 'bingo_app/master_board.html', context)

# def bingo_cartella_view(request):
#     board = generate_bingo_cartella()
#     return render(request, 'bingo_app/bingo_cartella.html', {'board': board})
