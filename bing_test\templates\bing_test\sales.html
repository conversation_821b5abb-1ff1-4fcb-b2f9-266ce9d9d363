{% extends 'bing_test/bing_test.html' %}

{% block page_title %}Sales{% endblock %}

{% block main_content %}
{% if current_fake_client %}
    <div class="mb-6 flex items-center justify-between">
        <div>
            <p class="text-gray-600">Sales analytics for <strong>{{ current_fake_client.name }}</strong></p>
        </div>
        <div class="flex space-x-3">
            <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
            </select>
            <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                Export Report
            </button>
        </div>
    </div>

    <!-- Sales Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-sm p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">Total Revenue</p>
                    <p class="text-2xl font-bold mt-1">$3,245.50</p>
                </div>
                <div class="p-3 bg-white bg-opacity-20 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-200 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="text-green-100 text-sm">+12.5% from last month</span>
            </div>
        </div>

        <!-- System Charges -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">System Charges</p>
                    <p class="text-2xl font-semibold text-gray-800 mt-1">$649.10</p>
                </div>
                <div class="p-3 rounded-lg bg-red-50 text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-sm text-gray-500">20% of total sales</span>
            </div>
        </div>

        <!-- Client Commission -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Client Commission</p>
                    <p class="text-2xl font-semibold text-gray-800 mt-1">$421.92</p>
                </div>
                <div class="p-3 rounded-lg bg-yellow-50 text-yellow-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-sm text-gray-500">13% of total sales</span>
            </div>
        </div>

        <!-- Net Prizes -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Net Prizes</p>
                    <p class="text-2xl font-semibold text-gray-800 mt-1">$2,174.48</p>
                </div>
                <div class="p-3 rounded-lg bg-purple-50 text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <span class="text-sm text-gray-500">67% of total sales</span>
            </div>
        </div>
    </div>

    <!-- Sales Chart and Recent Transactions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Sales Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Sales Trend</h4>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p class="text-gray-500">Sales Chart</p>
                    <p class="text-sm text-gray-400">Chart visualization would go here</p>
                </div>
            </div>
        </div>

        <!-- Top Performing Games -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Top Performing Games</h4>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            1
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Game #045</p>
                            <p class="text-sm text-gray-500">BINGO-75 • 32 cartellas</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">$480.00</p>
                        <p class="text-sm text-gray-500">Revenue</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            2
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Game #042</p>
                            <p class="text-sm text-gray-500">BINGO-75 • 28 cartellas</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">$420.00</p>
                        <p class="text-sm text-gray-500">Revenue</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            3
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Game #038</p>
                            <p class="text-sm text-gray-500">BINGO-75 • 25 cartellas</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">$375.00</p>
                        <p class="text-sm text-gray-500">Revenue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Breakdown Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800">Financial Breakdown</h4>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Game ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Stake</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">System Charge</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client Cat</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prize/Winner</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#045</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$480.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$96.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$62.40</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$321.60</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$107.20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 11, 2024</td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#044</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$350.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$70.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$45.50</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$234.50</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$117.25</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 10, 2024</td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#043</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$275.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$55.00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$35.75</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$184.25</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$92.13</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 9, 2024</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

{% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Client Selected</h4>
        <p class="text-gray-600 mb-6">Please select a fake client from the sidebar to view sales analytics.</p>
    </div>
{% endif %}
{% endblock %}
