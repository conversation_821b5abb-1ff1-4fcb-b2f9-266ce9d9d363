<!-- Test Header -->
<header class="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
    <div class="flex items-center">
        <!-- Mobile menu button -->
        <button class="md:hidden mr-4 text-gray-500" id="mobile-menu-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>
        
        <!-- Page Title -->
        <div class="flex items-center">
            <h2 class="text-xl font-semibold text-gray-800">
                {% block page_title %}Test Environment{% endblock %}
            </h2>
            <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Testing Mode
            </span>
        </div>
    </div>
    
    <!-- Header Actions -->
    <div class="flex items-center space-x-3">
        <!-- Current Client Info -->
        {% if current_fake_client %}
        <div class="hidden md:flex items-center space-x-2 px-3 py-1 bg-red-50 rounded-lg">
            <div class="w-6 h-6 rounded-full bg-gradient-to-r from-red-500 to-yellow-500 flex items-center justify-center">
                <span class="text-white font-bold text-xs">{{ current_fake_client.name|first }}</span>
            </div>
            <div class="text-sm">
                <span class="font-medium text-red-800">{{ current_fake_client.name }}</span>
                <span class="text-red-600 ml-1">({{ current_fake_client.shop_name }})</span>
            </div>
        </div>
        {% endif %}
        
        <!-- Test Actions -->
        <div class="flex items-center space-x-2">
            <!-- Refresh Data Button -->
            <button class="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors" 
                    title="Refresh Test Data"
                    onclick="window.location.reload()">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
            </button>
            
            <!-- Test Settings Button -->
            <button class="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors" 
                    title="Test Settings">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
    </div>
</header>

<!-- JavaScript for header interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (if needed)
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', function() {
            // Toggle mobile sidebar visibility
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.classList.toggle('hidden');
                sidebar.classList.toggle('md:block');
            }
        });
    }
});
</script>
