from django.contrib import admin
from .models import SystemConfig, FakeClient, Game, GameCartellaDetail

@admin.register(FakeClient)
class FakeClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'shop_name', 'phone_number', 'email', 'balance', 'is_active', 'is_superuser', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'shop_name', 'email')
    readonly_fields = ('created_at',)

@admin.register(Game)
class GameAdmin(admin.ModelAdmin):
    list_display = ('id', 'client', 'status', 'bet_price', 'sold_cartellas_count', 'winning_cartellas_count', 'total_stake', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('client__name', 'client__shop_name')
    readonly_fields = ('created_at',)

@admin.register(GameCartellaDetail)
class GameCartellaDetailAdmin(admin.ModelAdmin):
    list_display = ('game', 'cartella', 'is_winner', 'created_at')
    list_filter = ('is_winner', 'created_at')
    readonly_fields = ('created_at',)

@admin.register(SystemConfig)
class SystemConfigAdmin(admin.ModelAdmin):
    list_display = ('system_charge_rate', 'client_cat_rate', 'updated_at')
    readonly_fields = ('updated_at',)