<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BellaBingo - Game Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #DC2626 0%, #EAB308 100%);
        }
        .cartella-number {
            transition: all 0.2s ease-in-out;
        }
        .cartella-number:hover {
            transform: scale(1.05);
        }
        .cartella-selected {
            background: linear-gradient(135deg, #DC2626 0%, #EAB308 100%);
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }
        .cartella-available {
            background: white;
            border: 2px solid #e5e7eb;
            color: #374151;
        }
        .cartella-available:hover {
            border-color: #DC2626;
            background: #fef2f2;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Game Header -->
    <header class="gradient-bg text-white py-4 px-6 shadow-lg">
        <div class="flex items-center justify-between">
            <!-- Left: Game Title and Client Info -->
            <div class="flex items-center space-x-6">
                <div>
                    <h1 class="text-2xl font-bold">BellaBingo Game Interface</h1>
                    {% if current_fake_client %}
                    <p class="text-red-100 text-sm">
                        Host: {{ current_fake_client.name }} ({{ current_fake_client.shop_name }})
                    </p>
                    {% endif %}
                </div>
                
                <!-- Game Stats -->
                <div class="flex items-center space-x-6 text-sm">
                    <div class="bg-white bg-opacity-20 rounded-lg px-3 py-2">
                        <span class="text-red-100">Selected Cartellas:</span>
                        <span class="font-bold text-white text-lg ml-2" id="selected-count">0</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-3 py-2">
                        <span class="text-red-100">Total Revenue:</span>
                        <span class="font-bold text-white text-lg ml-2" id="total-revenue">$0</span>
                    </div>
                </div>
            </div>
            
            <!-- Right: Game Controls -->
            <div class="flex items-center space-x-3">
                <!-- Price per Cartella -->
                <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                    <label class="text-red-100 text-sm block">Price per Cartella</label>
                    <div class="flex items-center space-x-2 mt-1">
                        <button type="button" id="price-decrease" class="w-8 h-8 bg-white bg-opacity-30 rounded-full flex items-center justify-center hover:bg-opacity-40 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        <input type="number" 
                               id="bet-price" 
                               value="20" 
                               min="10" 
                               step="10"
                               class="w-20 px-2 py-1 text-center bg-white bg-opacity-30 border border-white border-opacity-30 rounded text-white placeholder-red-200 focus:outline-none focus:bg-opacity-40">
                        <button type="button" id="price-increase" class="w-8 h-8 bg-white bg-opacity-30 rounded-full flex items-center justify-center hover:bg-opacity-40 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <button id="start-game" class="px-6 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                    </svg>
                    <span>Start Game</span>
                </button>
                
                <button id="pause-game" class="px-4 py-3 bg-yellow-600 text-white rounded-lg font-semibold hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </button>
                
                <button id="cancel-game" class="px-4 py-3 bg-red-600 text-white rounded-lg font-semibold hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293-1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </button>
                
                <a href="{% url 'bing_test:dashboard' %}" class="px-4 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0L3.586 10l4.707-4.707a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </header>

    <!-- Quick Selection Form -->
    <div class="bg-white border-b border-gray-200 py-4 px-6">
        <div class="flex items-center justify-center space-x-4">
            <label for="quick-select" class="text-sm font-medium text-gray-700">Quick Select Cartella:</label>
            <form id="quick-select-form" class="flex items-center space-x-2">
                <input type="number" 
                       id="quick-select" 
                       placeholder="Enter cartella number..."
                       class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500 w-48">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                    Select
                </button>
            </form>
            <div class="text-sm text-gray-500">
                or click on cartella numbers below
            </div>
        </div>
    </div>

    <!-- Main Content: Cartella Numbers Display -->
    <main class="flex-1 p-6">
        {% if cartellas %}
        <div class="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 xl:grid-cols-20 gap-3" id="cartellas-grid">
            {% for cartella in cartellas %}
            <div class="cartella-number cartella-available rounded-lg p-4 text-center cursor-pointer border-2 transition-all duration-200" 
                 data-cartella="{{ cartella.cartella_number }}"
                 title="Click to select cartella #{{ cartella.cartella_number }}">
                <div class="text-2xl font-bold">{{ cartella.cartella_number }}</div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">No Cartellas Available</h3>
            <p class="text-gray-600">No cartellas found in the database.</p>
        </div>
        {% endif %}
    </main>

    <!-- Game Status Footer -->
    <footer class="bg-white border-t border-gray-200 py-3 px-6">
        <div class="flex items-center justify-between text-sm text-gray-600">
            <div>
                Total Available Cartellas: <span class="font-semibold">{{ total_cartellas }}</span>
            </div>
            <div id="game-status" class="font-medium">
                Ready to start game
            </div>
            <div>
                Game Mode: <span class="font-semibold">BINGO-75</span>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Game Interface -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game state
            let selectedCartellas = new Set();
            let gameStarted = false;
            let betPrice = {{ default_bet_price }};

            // DOM elements
            const selectedCountEl = document.getElementById('selected-count');
            const totalRevenueEl = document.getElementById('total-revenue');
            const betPriceInput = document.getElementById('bet-price');
            const quickSelectForm = document.getElementById('quick-select-form');
            const quickSelectInput = document.getElementById('quick-select');
            const startGameBtn = document.getElementById('start-game');
            const pauseGameBtn = document.getElementById('pause-game');
            const cancelGameBtn = document.getElementById('cancel-game');
            const gameStatusEl = document.getElementById('game-status');
            const priceDecreaseBtn = document.getElementById('price-decrease');
            const priceIncreaseBtn = document.getElementById('price-increase');

            // Initialize
            updateDisplay();

            // Price controls
            priceDecreaseBtn.addEventListener('click', function() {
                const currentPrice = parseInt(betPriceInput.value) || {{ default_bet_price }};
                const newPrice = Math.max({{ min_bet_price }}, currentPrice - {{ bet_increment }});
                betPriceInput.value = newPrice;
                betPrice = newPrice;
                updateDisplay();
            });

            priceIncreaseBtn.addEventListener('click', function() {
                const currentPrice = parseInt(betPriceInput.value) || {{ default_bet_price }};
                const newPrice = currentPrice + {{ bet_increment }};
                betPriceInput.value = newPrice;
                betPrice = newPrice;
                updateDisplay();
            });

            betPriceInput.addEventListener('change', function() {
                let value = parseInt(this.value) || {{ default_bet_price }};
                value = Math.max({{ min_bet_price }}, value);
                // Round to nearest increment
                value = Math.round(value / {{ bet_increment }}) * {{ bet_increment }};
                this.value = value;
                betPrice = value;
                updateDisplay();
            });

            // Quick select form
            quickSelectForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const cartellaNumber = quickSelectInput.value.trim();
                if (cartellaNumber) {
                    selectCartella(cartellaNumber);
                    quickSelectInput.value = '';
                }
            });

            // Cartella click handlers
            document.querySelectorAll('.cartella-number').forEach(function(element) {
                element.addEventListener('click', function() {
                    const cartellaNumber = this.dataset.cartella;
                    selectCartella(cartellaNumber);
                });
            });

            // Game control buttons
            startGameBtn.addEventListener('click', function() {
                if (selectedCartellas.size === 0) {
                    alert('Please select at least one cartella to start the game.');
                    return;
                }

                if (confirm(`Start game with ${selectedCartellas.size} cartellas at $${betPrice} each?`)) {
                    startGame();
                }
            });

            pauseGameBtn.addEventListener('click', function() {
                pauseGame();
            });

            cancelGameBtn.addEventListener('click', function() {
                if (confirm('Cancel game and clear all selections?')) {
                    cancelGame();
                }
            });

            // Functions
            function selectCartella(cartellaNumber) {
                const element = document.querySelector(`[data-cartella="${cartellaNumber}"]`);
                if (!element) {
                    alert(`Cartella #${cartellaNumber} not found.`);
                    return;
                }

                if (selectedCartellas.has(cartellaNumber)) {
                    // Deselect
                    selectedCartellas.delete(cartellaNumber);
                    element.classList.remove('cartella-selected');
                    element.classList.add('cartella-available');
                } else {
                    // Select
                    selectedCartellas.add(cartellaNumber);
                    element.classList.remove('cartella-available');
                    element.classList.add('cartella-selected');
                }

                updateDisplay();
            }

            function updateDisplay() {
                const count = selectedCartellas.size;
                const revenue = count * betPrice;

                selectedCountEl.textContent = count;
                totalRevenueEl.textContent = `$${revenue.toLocaleString()}`;

                // Update game status
                if (gameStarted) {
                    gameStatusEl.textContent = 'Game in progress';
                } else if (count > 0) {
                    gameStatusEl.textContent = `Ready to start with ${count} cartella${count !== 1 ? 's' : ''}`;
                } else {
                    gameStatusEl.textContent = 'Select cartellas to start game';
                }
            }

            function startGame() {
                gameStarted = true;
                startGameBtn.disabled = true;
                pauseGameBtn.disabled = false;

                // Disable cartella selection
                document.querySelectorAll('.cartella-number').forEach(function(element) {
                    element.style.pointerEvents = 'none';
                    element.style.opacity = '0.7';
                });

                // Disable price controls
                betPriceInput.disabled = true;
                priceDecreaseBtn.disabled = true;
                priceIncreaseBtn.disabled = true;
                quickSelectInput.disabled = true;

                updateDisplay();

                // Here you would typically send the game data to the server
                console.log('Game started with cartellas:', Array.from(selectedCartellas));
                console.log('Bet price:', betPrice);

                alert(`Game started successfully!\nCartellas: ${selectedCartellas.size}\nTotal Revenue: $${(selectedCartellas.size * betPrice).toLocaleString()}`);
            }

            function pauseGame() {
                gameStarted = false;
                startGameBtn.disabled = false;
                pauseGameBtn.disabled = true;

                updateDisplay();
                alert('Game paused. You can resume by clicking Start Game.');
            }

            function cancelGame() {
                gameStarted = false;
                selectedCartellas.clear();

                // Reset UI
                document.querySelectorAll('.cartella-selected').forEach(function(element) {
                    element.classList.remove('cartella-selected');
                    element.classList.add('cartella-available');
                });

                // Re-enable controls
                document.querySelectorAll('.cartella-number').forEach(function(element) {
                    element.style.pointerEvents = 'auto';
                    element.style.opacity = '1';
                });

                betPriceInput.disabled = false;
                priceDecreaseBtn.disabled = false;
                priceIncreaseBtn.disabled = false;
                quickSelectInput.disabled = false;
                startGameBtn.disabled = false;
                pauseGameBtn.disabled = true;

                updateDisplay();
            }
        });
    </script>
