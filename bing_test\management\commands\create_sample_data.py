from django.core.management.base import BaseCommand
from bing_test.models import FakeClient, SystemConfig
import random
from decimal import Decimal

class Command(BaseCommand):
    help = 'Create sample FakeClient data for testing'

    def handle(self, *args, **options):
        # Create SystemConfig if it doesn't exist
        system_config, created = SystemConfig.objects.get_or_create(
            defaults={
                'system_charge_rate': Decimal('0.20'),  # 20%
                'client_cat_rate': Decimal('0.13'),     # 13%
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('Created SystemConfig'))

        # Sample client data
        sample_clients = [
            {
                'name': '<PERSON>',
                'shop_name': 'Lucky Stars Bingo',
                'phone_number': '******-0101',
                'email': '<EMAIL>',
                'balance': Decimal('1250.75')
            },
            {
                'name': '<PERSON>',
                'shop_name': 'Golden Numbers',
                'phone_number': '******-0102',
                'email': '<EMAIL>',
                'balance': Decimal('890.50')
            },
            {
                'name': '<PERSON>',
                'shop_name': 'Dragon Bingo Palace',
                'phone_number': '******-0103',
                'email': '<EMAIL>',
                'balance': Decimal('2100.25')
            },
            {
                'name': 'Ahmed Hassan',
                'shop_name': 'Desert Oasis Games',
                'phone_number': '******-0104',
                'email': '<EMAIL>',
                'balance': Decimal('675.00')
            },
            {
                'name': 'Isabella Rossi',
                'shop_name': 'Bella Fortuna',
                'phone_number': '******-0105',
                'email': '<EMAIL>',
                'balance': Decimal('1450.80')
            },
            {
                'name': 'David Kim',
                'shop_name': 'Seoul Bingo Center',
                'phone_number': '******-0106',
                'email': '<EMAIL>',
                'balance': Decimal('980.30')
            },
            {
                'name': 'Emma Johnson',
                'shop_name': 'Royal Bingo Hall',
                'phone_number': '******-0107',
                'email': '<EMAIL>',
                'balance': Decimal('1750.60')
            },
            {
                'name': 'Carlos Mendoza',
                'shop_name': 'El Dorado Games',
                'phone_number': '******-0108',
                'email': '<EMAIL>',
                'balance': Decimal('520.45')
            },
            {
                'name': 'Priya Patel',
                'shop_name': 'Mumbai Bingo Express',
                'phone_number': '******-0109',
                'email': '<EMAIL>',
                'balance': Decimal('1320.90')
            },
            {
                'name': 'Michael O\'Connor',
                'shop_name': 'Irish Luck Bingo',
                'phone_number': '******-0110',
                'email': '<EMAIL>',
                'balance': Decimal('775.25')
            }
        ]

        created_count = 0
        for client_data in sample_clients:
            client, created = FakeClient.objects.get_or_create(
                email=client_data['email'],
                defaults=client_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created client: {client.name} ({client.shop_name})')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Client already exists: {client.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} new fake clients')
        )
        
        total_clients = FakeClient.objects.filter(is_active=True).count()
        self.stdout.write(
            self.style.SUCCESS(f'Total active fake clients: {total_clients}')
        )
