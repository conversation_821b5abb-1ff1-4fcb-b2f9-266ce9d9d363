from django.core.management.base import BaseCommand
from bing_test.models import FakeClient, SystemConfig
import random
from decimal import Decimal

class Command(BaseCommand):
    help = 'Create sample FakeClient data for testing'

    def handle(self, *args, **options):
        # Create SystemConfig if it doesn't exist
        system_config, created = SystemConfig.objects.get_or_create(
            defaults={
                'system_charge_rate': Decimal('0.20'),  # 20%
                'client_cat_rate': Decimal('0.13'),     # 13%
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('Created SystemConfig'))

        # Sample client data (mix of admin and regular users)
        sample_clients = [
            {
                'name': 'Admin User',
                'shop_name': 'BellaBingo Admin',
                'phone_number': '******-0001',
                'email': '<EMAIL>',
                'balance': Decimal('5000.00'),
                'is_superuser': True,
                'address': '123 Admin Street, Admin City, AC 12345'
            },
            {
                'name': 'Super Ad<PERSON>',
                'shop_name': 'System Administrator',
                'phone_number': '******-0002',
                'email': '<EMAIL>',
                'balance': Decimal('10000.00'),
                'is_superuser': True,
                'address': '456 Super Admin Ave, Admin City, AC 12345'
            },
            {
                'name': 'Maria Rodriguez',
                'shop_name': 'Lucky Stars Bingo',
                'phone_number': '******-0101',
                'email': '<EMAIL>',
                'balance': Decimal('1250.75'),
                'is_superuser': False,
                'address': '789 Lucky Street, Star City, SC 67890'
            },
            {
                'name': 'John Thompson',
                'shop_name': 'Golden Numbers',
                'phone_number': '******-0102',
                'email': '<EMAIL>',
                'balance': Decimal('890.50'),
                'is_superuser': False,
                'address': '321 Golden Ave, Number Town, NT 54321'
            },
            {
                'name': 'Sofia Chen',
                'shop_name': 'Dragon Bingo Palace',
                'phone_number': '******-0103',
                'email': '<EMAIL>',
                'balance': Decimal('2100.25'),
                'is_superuser': False,
                'address': '654 Dragon Road, Palace City, PC 98765'
            },
            {
                'name': 'Ahmed Hassan',
                'shop_name': 'Desert Oasis Games',
                'phone_number': '******-0104',
                'email': '<EMAIL>',
                'balance': Decimal('675.00'),
                'is_superuser': False,
                'address': '987 Oasis Blvd, Desert Town, DT 13579'
            },
            {
                'name': 'Isabella Rossi',
                'shop_name': 'Bella Fortuna',
                'phone_number': '******-0105',
                'email': '<EMAIL>',
                'balance': Decimal('1450.80'),
                'is_superuser': False,
                'address': '147 Fortuna Street, Bella City, BC 24680'
            },
            {
                'name': 'David Kim',
                'shop_name': 'Seoul Bingo Center',
                'phone_number': '******-0106',
                'email': '<EMAIL>',
                'balance': Decimal('980.30'),
                'is_superuser': False,
                'address': '258 Seoul Avenue, Bingo District, BD 36912'
            },
            {
                'name': 'Emma Johnson',
                'shop_name': 'Royal Bingo Hall',
                'phone_number': '******-0107',
                'email': '<EMAIL>',
                'balance': Decimal('1750.60'),
                'is_superuser': False,
                'address': '369 Royal Road, Hall City, HC 47025'
            },
            {
                'name': 'Carlos Mendoza',
                'shop_name': 'El Dorado Games',
                'phone_number': '******-0108',
                'email': '<EMAIL>',
                'balance': Decimal('520.45'),
                'is_superuser': False,
                'address': '741 Dorado Street, Game Town, GT 58136'
            },
            {
                'name': 'Priya Patel',
                'shop_name': 'Mumbai Bingo Express',
                'phone_number': '******-0109',
                'email': '<EMAIL>',
                'balance': Decimal('1320.90'),
                'is_superuser': False,
                'address': '852 Express Lane, Mumbai District, MD 69247'
            },
            {
                'name': 'Michael O\'Connor',
                'shop_name': 'Irish Luck Bingo',
                'phone_number': '******-0110',
                'email': '<EMAIL>',
                'balance': Decimal('775.25'),
                'is_superuser': False,
                'address': '963 Luck Avenue, Irish Quarter, IQ 70358'
            }
        ]

        created_count = 0
        for client_data in sample_clients:
            client, created = FakeClient.objects.get_or_create(
                email=client_data['email'],
                defaults=client_data
            )
            if created:
                created_count += 1
                role = "Admin" if client.is_superuser else "Regular"
                self.stdout.write(
                    self.style.SUCCESS(f'Created {role} client: {client.name} ({client.shop_name})')
                )
            else:
                role = "Admin" if client.is_superuser else "Regular"
                self.stdout.write(
                    self.style.WARNING(f'{role} client already exists: {client.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} new fake clients')
        )

        total_clients = FakeClient.objects.filter(is_active=True).count()
        admin_clients = FakeClient.objects.filter(is_active=True, is_superuser=True).count()
        regular_clients = FakeClient.objects.filter(is_active=True, is_superuser=False).count()

        self.stdout.write(
            self.style.SUCCESS(f'Total active fake clients: {total_clients}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'  - Admin clients: {admin_clients}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'  - Regular clients: {regular_clients}')
        )
