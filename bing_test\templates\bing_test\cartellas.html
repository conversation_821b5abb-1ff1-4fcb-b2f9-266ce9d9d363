{% extends 'bing_test/bing_test.html' %}

{% block page_title %}Cartellas{% endblock %}

{% block main_content %}
{% if current_fake_client %}
    <div class="mb-6 flex items-center justify-between">
        <div>
            <p class="text-gray-600">Manage cartellas for <strong>{{ current_fake_client.name }}</strong></p>
        </div>
        <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
            Generate New Cartella
        </button>
    </div>

    <!-- Cartella Generator -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">Cartella Generator</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <label for="cartella_count" class="block text-sm font-medium text-gray-700 mb-2">
                    Number of Cartellas
                </label>
                <input type="number" 
                       id="cartella_count" 
                       min="1" 
                       max="100" 
                       value="1"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Cartella Type
                </label>
                <select class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
                    <option value="bingo75">BINGO-75 (Standard)</option>
                    <option value="bingo90">BINGO-90 (European)</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors">
                    Generate Cartellas
                </button>
            </div>
        </div>
    </div>

    <!-- Sample Cartellas Display -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Sample Cartella 1 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-4 bg-red-50 border-b border-red-100">
                <div class="flex items-center justify-between">
                    <h5 class="font-semibold text-red-800">Cartella #001</h5>
                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">Active</span>
                </div>
            </div>
            
            <div class="p-4">
                <!-- BINGO Header -->
                <div class="grid grid-cols-5 gap-1 mb-2">
                    <div class="text-center font-bold text-red-600 py-1 text-sm">B</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">I</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">N</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">G</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">O</div>
                </div>
                
                <!-- Numbers Grid -->
                <div class="grid grid-cols-5 gap-1">
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">3</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">18</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">32</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">47</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">63</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">7</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">22</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">35</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">51</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">68</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">12</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">26</div>
                    <div class="bg-red-100 text-red-700 rounded p-2 text-center text-sm font-medium">FREE</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">55</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">71</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">9</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">19</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">41</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">58</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">74</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">15</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">29</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">43</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">60</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">75</div>
                </div>
            </div>
            
            <div class="p-4 bg-gray-50 border-t border-gray-100">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Created:</span>
                    <span class="font-medium text-gray-800">Today</span>
                </div>
            </div>
        </div>

        <!-- Sample Cartella 2 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-4 bg-red-50 border-b border-red-100">
                <div class="flex items-center justify-between">
                    <h5 class="font-semibold text-red-800">Cartella #002</h5>
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">In Game</span>
                </div>
            </div>
            
            <div class="p-4">
                <!-- BINGO Header -->
                <div class="grid grid-cols-5 gap-1 mb-2">
                    <div class="text-center font-bold text-red-600 py-1 text-sm">B</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">I</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">N</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">G</div>
                    <div class="text-center font-bold text-red-600 py-1 text-sm">O</div>
                </div>
                
                <!-- Numbers Grid with some called numbers -->
                <div class="grid grid-cols-5 gap-1">
                    <div class="bg-red-200 text-red-800 rounded p-2 text-center text-sm font-medium">5</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">16</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">31</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">46</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">61</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">8</div>
                    <div class="bg-red-200 text-red-800 rounded p-2 text-center text-sm font-medium">23</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">38</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">52</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">67</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">11</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">27</div>
                    <div class="bg-red-100 text-red-700 rounded p-2 text-center text-sm font-medium">FREE</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">56</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">72</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">14</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">25</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">39</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">59</div>
                    <div class="bg-red-200 text-red-800 rounded p-2 text-center text-sm font-medium">73</div>
                    
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">2</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">28</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">44</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">57</div>
                    <div class="bg-gray-50 rounded p-2 text-center text-sm font-medium">69</div>
                </div>
            </div>
            
            <div class="p-4 bg-gray-50 border-t border-gray-100">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Game:</span>
                    <span class="font-medium text-gray-800">#123</span>
                </div>
            </div>
        </div>

        <!-- Add New Cartella Card -->
        <div class="bg-white rounded-xl shadow-sm border-2 border-dashed border-gray-300 flex items-center justify-center p-8 hover:border-red-400 hover:bg-red-50 transition-colors cursor-pointer">
            <div class="text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <h5 class="text-lg font-medium text-gray-800 mb-2">Generate New Cartella</h5>
                <p class="text-sm text-gray-600">Click to create a new bingo cartella</p>
            </div>
        </div>
    </div>

    <!-- Cartella Statistics -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Total Cartellas</p>
                    <p class="text-xl font-semibold text-gray-800">24</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Active</p>
                    <p class="text-xl font-semibold text-gray-800">18</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">In Games</p>
                    <p class="text-xl font-semibold text-gray-800">5</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Winners</p>
                    <p class="text-xl font-semibold text-gray-800">3</p>
                </div>
            </div>
        </div>
    </div>

{% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Client Selected</h4>
        <p class="text-gray-600 mb-6">Please select a fake client from the sidebar to manage cartellas.</p>
    </div>
{% endif %}
{% endblock %}
