{% extends 'bing_test/bing_test.html' %}

{% block page_title %}Cartellas{% endblock %}

{% block main_content %}
{% if current_fake_client %}
    <div class="mb-6 flex items-center justify-between">
        <div>
            <p class="text-gray-600">
                {% if is_admin %}
                    Manage cartellas for <strong>{{ current_fake_client.name }}</strong> (Admin)
                {% else %}
                    View cartellas for <strong>{{ current_fake_client.name }}</strong>
                {% endif %}
            </p>
            <p class="text-sm text-gray-500 mt-1">
                Total: {{ total_cartellas }} cartellas
                {% if search_query %} | Filtered results for "{{ search_query }}"{% endif %}
            </p>
        </div>
        {% if is_admin %}
        <button class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
            Generate New Cartella
        </button>
        {% endif %}
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-semibold text-gray-800">Search Cartellas</h4>
            {% if search_query %}
            <a href="{% url 'bing_test:cartellas' %}" class="text-sm text-red-600 hover:text-red-700">Clear Search</a>
            {% endif %}
        </div>

        <form method="GET" class="flex items-center space-x-4">
            <div class="flex-1">
                <input type="text"
                       name="search"
                       value="{{ search_query }}"
                       placeholder="Search by cartella number, ID, or grid numbers..."
                       class="block w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
            </div>
            <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                Search
            </button>
        </form>
    </div>

    {% if is_admin %}
    <!-- Cartella Generator (Admin Only) -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">Cartella Generator</h4>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <label for="cartella_count" class="block text-sm font-medium text-gray-700 mb-2">
                    Number of Cartellas
                </label>
                <input type="number"
                       id="cartella_count"
                       min="1"
                       max="100"
                       value="1"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Cartella Type
                </label>
                <select class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
                    <option value="bingo75">BINGO-75 (Standard)</option>
                    <option value="bingo90">BINGO-90 (European)</option>
                </select>
            </div>

            <div class="flex items-end">
                <button class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors">
                    Generate Cartellas
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Real Cartellas Display -->
    {% if cartellas %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {% for cartella in cartellas %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <!-- Cartella Header -->
            <div class="p-3 bg-red-50 border-b border-red-100">
                <div class="flex items-center justify-between">
                    <h5 class="font-semibold text-red-800 text-sm">
                        {% if is_admin %}
                            {{ cartella.cartella_id }}
                        {% endif %}
                        #{{ cartella.cartella_number }}
                    </h5>
                    {% if is_admin %}
                    <div class="flex space-x-1">
                        <button class="p-1 text-gray-500 hover:text-red-600 transition-colors" title="Edit">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                            </svg>
                        </button>
                        <button class="p-1 text-gray-500 hover:text-red-600 transition-colors" title="Delete">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L10 11.414l2.293-2.293a1 1 0 000-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Cartella Grid -->
            <div class="p-3">
                <!-- BINGO Header -->
                <div class="grid grid-cols-5 gap-1 mb-1">
                    <div class="text-center font-bold text-red-600 py-1 text-xs">B</div>
                    <div class="text-center font-bold text-red-600 py-1 text-xs">I</div>
                    <div class="text-center font-bold text-red-600 py-1 text-xs">N</div>
                    <div class="text-center font-bold text-red-600 py-1 text-xs">G</div>
                    <div class="text-center font-bold text-red-600 py-1 text-xs">O</div>
                </div>

                <!-- Numbers Grid -->
                <div class="grid grid-cols-5 gap-1">
                    {% for row in cartella.grid %}
                        {% for number in row %}
                        <div class="{% if number == 'FREE' %}bg-red-100 text-red-700{% else %}bg-gray-50{% endif %} rounded p-1 text-center text-xs font-medium">
                            {{ number }}
                        </div>
                        {% endfor %}
                    {% endfor %}
                </div>
            </div>

            <!-- Cartella Footer -->
            {% if is_admin %}
            <div class="p-3 bg-gray-50 border-t border-gray-100">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-gray-600">Created:</span>
                    <span class="font-medium text-gray-800">{{ cartella.created_at|date:"M d, Y" }}</span>
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Cartellas Found</h4>
        <p class="text-gray-600 mb-6">
            {% if search_query %}
                No cartellas match your search criteria.
            {% else %}
                No cartellas available in the database.
            {% endif %}
        </p>
        {% if search_query %}
        <a href="{% url 'bing_test:cartellas' %}" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            Clear Search
        </a>
        {% endif %}
    </div>
    {% endif %}


    <!-- Pagination -->
    {% if cartellas.has_other_pages %}
    <div class="mt-8 flex items-center justify-between">
        <div class="text-sm text-gray-500">
            Showing {{ cartellas.start_index }} to {{ cartellas.end_index }} of {{ cartellas.paginator.count }} cartellas
        </div>
        <div class="flex space-x-2">
            {% if cartellas.has_previous %}
                <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                    First
                </a>
                <a href="?page={{ cartellas.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                    Previous
                </a>
            {% endif %}

            <!-- Page numbers -->
            {% for num in cartellas.paginator.page_range %}
                {% if num == cartellas.number %}
                    <span class="px-3 py-1 bg-red-600 text-white rounded text-sm">{{ num }}</span>
                {% elif num > cartellas.number|add:'-3' and num < cartellas.number|add:'3' %}
                    <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}"
                       class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                        {{ num }}
                    </a>
                {% endif %}
            {% endfor %}

            {% if cartellas.has_next %}
                <a href="?page={{ cartellas.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                    Next
                </a>
                <a href="?page={{ cartellas.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                    Last
                </a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if is_admin %}
    <!-- Cartella Statistics (Admin Only) -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Total Cartellas</p>
                    <p class="text-xl font-semibold text-gray-800">{{ total_cartellas }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Current Page</p>
                    <p class="text-xl font-semibold text-gray-800">{{ cartellas|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Pages</p>
                    <p class="text-xl font-semibold text-gray-800">{{ cartellas.paginator.num_pages }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-gray-600">{% if search_query %}Filtered{% else %}All{% endif %}</p>
                    <p class="text-xl font-semibold text-gray-800">{{ cartellas.paginator.count }}</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

{% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Client Selected</h4>
        <p class="text-gray-600 mb-6">Please select a fake client from the sidebar to manage cartellas.</p>
    </div>
{% endif %}
{% endblock %}
