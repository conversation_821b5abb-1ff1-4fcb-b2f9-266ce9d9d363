{% extends 'bing_test/bing_test.html' %}

{% block page_title %}Dashboard{% endblock %}

{% block main_content %}
{% if current_fake_client %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Client Info Card -->
        <div class="bg-gradient-to-r from-red-500 to-yellow-500 rounded-xl shadow-sm p-6 text-white">
            <div class="flex items-center">
                <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-4">
                    <span class="text-2xl font-bold">{{ current_fake_client.name|first }}</span>
                </div>
                <div>
                    <h4 class="text-lg font-semibold">{{ current_fake_client.name }}</h4>
                    <p class="text-red-100">{{ current_fake_client.shop_name }}</p>
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-white border-opacity-20">
                <div class="flex justify-between items-center">
                    <span class="text-red-100">Balance:</span>
                    <span class="text-xl font-bold">${{ current_fake_client.balance|floatformat:2 }}</span>
                </div>
            </div>
        </div>

        <!-- Total Games Card -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Total Games</p>
                    <p class="text-2xl font-semibold text-gray-800 mt-1">{{ total_games|default:0 }}</p>
                </div>
                <div class="p-3 rounded-lg bg-blue-50 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Active Games Card -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Active Games</p>
                    <p class="text-2xl font-semibold text-gray-800 mt-1">{{ active_games|default:0 }}</p>
                </div>
                <div class="p-3 rounded-lg bg-green-50 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Games -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">Recent Games</h4>
        {% if recent_games %}
            <div class="space-y-3">
                {% for game in recent_games %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-2 rounded-lg mr-3
                                    {% if game.status == 'completed' %}bg-green-100 text-green-600
                                    {% elif game.status == 'playing' %}bg-blue-100 text-blue-600
                                    {% elif game.status == 'paused' %}bg-yellow-100 text-yellow-600
                                    {% elif game.status == 'cancelled' %}bg-red-100 text-red-600
                                    {% else %}bg-gray-100 text-gray-600{% endif %}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                {% if game.status == 'completed' %}
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                {% elif game.status == 'playing' %}
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                {% else %}
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                {% endif %}
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Game #{{ game.id }}</p>
                            <p class="text-sm text-gray-500">{{ game.status|title }} • {{ game.created_at|timesince }} ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-medium text-gray-800">${{ game.bet_price|floatformat:2 }}</p>
                        <p class="text-sm text-gray-500">{{ game.sold_cartellas_count }} cartellas</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p class="text-gray-500">No games found for this client</p>
                <a href="{% url 'bing_test:start_game' %}" class="mt-2 inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Start Your First Game
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a href="{% url 'bing_test:start_game' %}" class="flex items-center justify-center p-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
            Start New Game
        </a>
        <a href="{% url 'bing_test:cartellas' %}" class="flex items-center justify-center p-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            View Cartellas
        </a>
        <a href="{% url 'bing_test:games' %}" class="flex items-center justify-center p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
            Game History
        </a>
        <a href="{% url 'bing_test:sales' %}" class="flex items-center justify-center p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
            </svg>
            Sales Report
        </a>
    </div>

{% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Client Selected</h4>
        <p class="text-gray-600 mb-6">Please select a fake client from the sidebar to view dashboard data.</p>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
            <p class="text-sm text-yellow-800">
                <strong>Testing Mode:</strong> Use the "Switch Client" dropdown in the sidebar to simulate different users.
            </p>
        </div>
    </div>
{% endif %}
{% endblock %}
