I want to develop an online bingo gaming website. Shops, such as sports bars or groceries, can purchase a package and play a game online. They will have an admin page to see the number of games & sales. And the system owner can see the number of games/& sales for every shop and can track 20% of every game's income from every shop registered on the system.

Shops can set every card stake/bet value, and can decide winning patterns for every game. For example, on game#1, one card(called cartela) can be sold for 10birr, next time maybe 20Birr, and if the winning pattern is full-house, the cartela may be sold for 50 or more birr.

Requirements:
Every shop should have valid credentials on the system, and they buy packaged items based on balance until the packaged value is exhausted. so, another package will be purchased. If the balance is below the threshold warning, and if it is 0, block the Shop from playing the game.

The system should generate a unique 5x5 cartela(card) with middle free, as needed, but initially, 200 will be accessible for the shop. they will have a unique number, just starting from #1 to #200.
***
Game Flow:
Game flow is a shop register that sells cartelas on the system. The system shows the number of cartelas as players(note one player can have multiple cartelas, but the system counts the sold cartela as one player), 
- shop admin sees the total-bet-amount = (total cartela sold per game * price per cartela), 
- playesr see win-amount = total-bet-amount - (total-bet-amount * 33%), 20% for the system 13% for the shop per every game. 
- Then shop admin press the play button, the system register the game with unique number, and starts to draw rundom numbers, with calling sound and the game ends when some one say bingo, then shop admin will puse game, and check the cartella number, system check if the winning pattern is reached. and boom Bingo if yes & game over and winner is declared and next game starts, or if cartella is not winner, game continues.

Registering cartella can be copied/sysced from previos game to next game.

Every game should have its own winning pattern, and the system should have a default winning pattern, and the shop can change it for every game. evry game has its own unique number, winning pattern, and total-bet-amount, win-amount, and winner/cartella number/.

Dashboard for shop admin to see the number of games played, total-bet-amount, win-amount, and winner/cartella number.

## Lovable design

create a vibrant **BellaBingo** dashboard that captures the excitement of classic bingo halls with modern web design. This will feature a comprehensive dashboard with game management, real-time bingo board, cartella tracking, and sales analytics.

### Design Inspiration: 
Classic bingo hall aesthetics meets modern dashboard - think vibrant reds and golds with clean card layouts, smooth animations for number calling, and an intuitive sidebar navigation.

### Features for v1:

- Sidebar navigation with all sections
- Dashboard overview with key metrics
- BINGO-75 game board with number calling
- Cartella management (5x5 bingo cards)
- Games history and sales tracking
- Responsive design with smooth animations
- Design System: Warm bingo-inspired palette with red/gold primary colors, clean typography, and card-based layouts.

I have Created **BellaBingo** - a comprehensive bingo gaming dashboard with a vibrant red and gold color scheme that captures the excitement of classic bingo halls while maintaining modern usability.

### ✨ Key Features Implemented:
- 🎯 Complete Dashboard with real-time stats and quick actions
- 🎲 BINGO-75 Game Board with number calling and tracking
- 🎟️ Cartella Management with 5x5 cards and win detection
- 📊 Games Tracking with status monitoring and history
- 💰 Sales Analytics showing daily and monthly performance
- 📱 Responsive Sidebar navigation with mobile support

### 🎨 Design System:
- **Colors**: Warm bingo-inspired reds (#DC2626) and golds (#EAB308)
- **Layout**: Card-based design with subtle gradients
- **Typography**: Clean, readable fonts with proper contrast
- **Animations**: Smooth transitions and winner highlights
- **Mobile-First**: Fully responsive across all devices
* The app includes real bingo functionality - call numbers, track cartellas, detect wins, and manage sales. Each section is fully interactive and ready for real gaming operations!
