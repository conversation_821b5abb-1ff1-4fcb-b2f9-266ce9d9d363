{% extends 'bing_test/bing_test.html' %}

{% block page_title %}Start Game{% endblock %}

{% block main_content %}
{% if current_fake_client %}
    <div class="max-w-4xl mx-auto">
        <!-- Game Setup Form -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-6">Create New Game</h4>
            
            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Bet Price -->
                    <div>
                        <label for="bet_price" class="block text-sm font-medium text-gray-700 mb-2">
                            Bet Price per Cartella
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" 
                                   id="bet_price" 
                                   name="bet_price" 
                                   step="0.01" 
                                   min="0.01" 
                                   value="1.00"
                                   class="block w-full pl-7 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
                        </div>
                    </div>

                    <!-- Winning Patterns -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Winning Patterns
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="patterns" value="row" checked 
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Any Row</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="patterns" value="column" 
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Any Column</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="patterns" value="diagonal" 
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Diagonal</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="patterns" value="full_house" 
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Full House</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-500">
                        Client: <strong>{{ current_fake_client.name }}</strong> ({{ current_fake_client.shop_name }})
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" 
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                            Save as Draft
                        </button>
                        <button type="submit" 
                                class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors">
                            Start Game
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Game Preview -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Game Preview</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Sample Cartella -->
                <div>
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Sample Cartella</h5>
                    <div class="bg-red-50 rounded-lg p-4">
                        <!-- BINGO Header -->
                        <div class="grid grid-cols-5 gap-2 mb-2">
                            <div class="text-center font-bold text-red-600 py-1 text-sm">B</div>
                            <div class="text-center font-bold text-red-600 py-1 text-sm">I</div>
                            <div class="text-center font-bold text-red-600 py-1 text-sm">N</div>
                            <div class="text-center font-bold text-red-600 py-1 text-sm">G</div>
                            <div class="text-center font-bold text-red-600 py-1 text-sm">O</div>
                        </div>
                        
                        <!-- Sample Numbers -->
                        <div class="grid grid-cols-5 gap-2">
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">3</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">18</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">32</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">47</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">63</div>
                            
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">7</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">22</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">35</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">51</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">68</div>
                            
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">12</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">26</div>
                            <div class="bg-red-200 text-red-700 rounded p-2 text-center text-sm font-medium">FREE</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">55</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">71</div>
                            
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">9</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">19</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">41</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">58</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">74</div>
                            
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">15</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">29</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">43</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">60</div>
                            <div class="bg-white rounded p-2 text-center text-sm font-medium">75</div>
                        </div>
                    </div>
                </div>

                <!-- Game Settings Summary -->
                <div>
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Game Settings</h5>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">Bet Price:</span>
                            <span class="text-sm font-medium text-gray-800" id="preview-bet-price">$1.00</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">Client:</span>
                            <span class="text-sm font-medium text-gray-800">{{ current_fake_client.name }}</span>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">Winning Patterns:</span>
                            <div class="mt-1" id="preview-patterns">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-1">
                                    Any Row
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Preview -->
                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h6 class="text-sm font-medium text-yellow-800 mb-2">Financial Breakdown</h6>
                        <div class="space-y-1 text-xs text-yellow-700">
                            <div class="flex justify-between">
                                <span>System Charge (20%):</span>
                                <span>$0.20</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Client Cat (13%):</span>
                                <span>$0.13</span>
                            </div>
                            <div class="flex justify-between font-medium border-t border-yellow-300 pt-1">
                                <span>Net Prize Pool:</span>
                                <span>$0.67</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% else %}
    <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h4 class="text-xl font-semibold text-gray-800 mb-2">No Client Selected</h4>
        <p class="text-gray-600 mb-6">Please select a fake client from the sidebar to start a game.</p>
    </div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview when bet price changes
    const betPriceInput = document.getElementById('bet_price');
    const previewBetPrice = document.getElementById('preview-bet-price');
    
    if (betPriceInput && previewBetPrice) {
        betPriceInput.addEventListener('input', function() {
            previewBetPrice.textContent = '$' + parseFloat(this.value || 0).toFixed(2);
        });
    }
    
    // Update patterns preview
    const patternCheckboxes = document.querySelectorAll('input[name="patterns"]');
    const previewPatterns = document.getElementById('preview-patterns');
    
    function updatePatternsPreview() {
        if (!previewPatterns) return;
        
        const selectedPatterns = Array.from(patternCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => {
                const labels = {
                    'row': 'Any Row',
                    'column': 'Any Column',
                    'diagonal': 'Diagonal',
                    'full_house': 'Full House'
                };
                return labels[cb.value] || cb.value;
            });
        
        if (selectedPatterns.length === 0) {
            previewPatterns.innerHTML = '<span class="text-sm text-gray-500">No patterns selected</span>';
        } else {
            previewPatterns.innerHTML = selectedPatterns.map(pattern => 
                `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-1 mb-1">${pattern}</span>`
            ).join('');
        }
    }
    
    patternCheckboxes.forEach(cb => {
        cb.addEventListener('change', updatePatternsPreview);
    });
    
    updatePatternsPreview();
});
</script>
{% endblock %}
